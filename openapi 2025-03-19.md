---
title: Open API简介

language_tabs: # must be one of https://github.com/rouge-ruby/rouge/wiki/List-of-supported-languages-and-lexers
  - shell
  - go
  - python
  - java

toc_footers:
  - <a href='#'>获取AK&SK</a>
  - <a href='http://aioc.taopower.ai'>算力调度平台</a>
  - <a href='http://www.taopower.ai'>上海市算力综合服务平台</a>

includes:
  - errors

search: true

code_clipboard: true

meta:
  - name: description
    content: Documentation for the TaoPower API
---

# 简介

你好，欢迎来到Open API!

你可以使用这里提供的API来访问、更新资源信息等。

我们提供了使用Shell、Go、Python和JavaScript等工具的示例!您可以在右侧的黑暗区域查看，并且可以通过右上方的选项卡切换示例的编程语言。



# 快速上手

## 环境准备

### MTLS证书授权

因安全合规要求，本系统暂定使用MTLS双向证书认证，即需要前端访问以及使用OpenAPI，需要获取本方授予的证书（前端访问通过浏览器安装，后端API则通过HTTP客户端库关联即可）；其中授权证书需要提供出口 IP 列表，基于对应的出口 IP 授权用于前端访问以及使用OpenAPI。

## 第一步：获取AK&SK

### AK&SK简介

AK(Access Key)&SK(Secret Key)是对用户调用行为进行鉴权和认证的工具。AK&SK相当于用户调用OpenAPI的专有用户名和密码

### 获取方法

获取AK&SK是使用OpenAPI的第一步，具体的操作方法如下：

1. 登录[算力调度](https://shanghaiai.com/)后，点击用户头像，选择Access key，进入[Access key管理界面](#).

2. 点击“生成”按钮，创建一对AK&SK，会自动添加在下方表格中。SK默认是隐藏状态，点击“眼睛”图标可以显示。

3. 点击一串AK/SK值，自动复制到粘贴板。然后就可以利用AK/SK进行下一步的操作啦。

## 第二步：生成签名

### 签名简介

为了防止API调用过程中被黑客恶意篡改，调用任何一个API都需要携带签名，服务端会根据请求参数，对签名进行验证，签名不合法的请求将会被拒绝。

签名相关参数(包含表示身份的`accessKey`和请求的时刻`ts`和签名`sign`)通过Header传递。

### 请求说明

```
Scheme: HTTP/HTTPS
Method: POST/GET
Content-Type: application/json
Host: http://openapi.taopower.ai
Path: /api/v1/a-example-path?q1=v1
Body: a-example-payload

Headers: {
    "ts": "{{STRING}}",
    "sign": "{{STRING}}",
    "accessKey": "{{STRING}}"
}

```

### Headers 参数

| 参数 | 数据类型 | 说明 | 是否必填 |
| -------- | ------- | ------- | ------- |
| ts | integer	| 请求时的毫秒级[Unix timestamp](https://tool.chinaz.com/tools/unixtime.aspx)，用于防止重放攻击 | 是 |
| sign | string |根据签名规则（下文）生成的签名	| 是 |
| accessKey	| string |根据[第一步](#第一步：获取AK&SK)：获取AK&SK生成的处于生效状态的accessKey | 是 |

<aside class="notice">
后续的所有接口，除明确说明，均需要携带这三个header。
</aside>


### 签名规则


当前采用 HMAC-SHA256 算法,通过校验签名进行鉴权，规范且安全性更好。

签名大体过程如下：

1. 如果有查询参数, 根据参数名称的ASCII码表的`字典顺序`排序,并依次拼接。如：`somepath?d=1&b=2&a=3&c=4` 得到QS = `a3b2c4d1`。 无则 `$QS`为空。
2. 如果有body, 绝大部分为Json，需要序列化为字节流, 无则空， 得到 `$BODY`
2. 通过请求时间戳 + accessKey + 查询参数 + 请求 body 进行字符拼接，得到原始的待签名字符串：
待签名字符串 `$TOSIGN = ${ts} + ${accessKey} + ${QS} + ${BODY}`
3. 通过 secretKey 作为密钥，对原始待签名字符串进行签名，生成得到签名：
`sign = Hex(HMAC_SHA256(secretKey, 待签名字符串))`

### 示例

如下请求报文：

```
POST /api/v1/a-example-path?d=1&b=2&a=3&c=4 HTTP/1.1
Host: http://openapi.taopower.ai
Content-Type: application/json
accessKey: a-example-accesskey
ts: 1723448959001
sign: a-dummy-sign
{"dummy": "val"}
```

#### 待签名字符串
1. $QS=`a1v2a1v1`
2. $BODY=`{"dummy": "val"}`
3. $TOSIGN=`1723448959001a-example-accesskeya3b2c4d1{"dummy": "val"}`

#### 签名
根据密钥通过 HMAC-SHA256 算法，生成十六进制 hash，其中示例对应 $secretKey =`a-dummy-secretkey`
```
sign= hmac-sha256($secretKey, $TOSIGN)
sign="1bc1deec54ca3f37e3008ce2378f8a7f2b36a6bb48153b47e307dca9b85ec86b"
```

参考：[hmac-sha256-online](https://www.devglan.com/online-tools/hmac-sha256-online)

> hmac参考如下代码

```python
#!/usr/bin/env python
import hmac
import base64
from hashlib import sha256

s = 'dummy'
key = 'dummy'
hashcode = hmac.new(bytes(key, "utf-8"), bytes(s, "utf-8"),
                        digestmod=sha256).hexdigest()

```

```go
import (
   "crypto/hmac"
   "crypto/sha256"
   "encoding/base64"
   "encoding/hex"
   "testing"
)

func TestSign(t *testing.T) {
   requestBody := "dummy"
   secretKey := "dmmy"

   h := hmac.New(sha256.New, []byte(secretKey))
   h.Write([]byte(requestBody))
   sha := hex.EncodeToString(h.Sum(nil))
   println(sign)
}

```

```java
package com.tencent.xg;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Hex;

public class SignTest {
    public static void main(String[] args) {
        try {
            String stringToSign = "dummy";
            String appSecret = "dummy";

            Mac mac;
            mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(appSecret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signatureBytes = mac.doFinal(stringToSign.getBytes("UTF-8"));

            String hexStr = Hex.encodeHexString(signatureBytes);

            System.out.println(hexStr);
        } catch (NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }
}

```

<aside class="warning">
注意替换 <code>appSecret</code>。
</aside>

## 第三步：上传文件

<aside class="notice">
目前无，暂略。
</aside>

## 第四步：调用API

<aside class="success">
可见后续单个接口。
</aside>

## 第五步：获取调用结果

<aside class="notice">
本段描述如何异步获取结果，用于阻塞过久的接口。暂略。
</aside>


# 算力调度

接口服务器：*`http://example.com`*  **待定**。

## 更新产品实时数据(优先级P1)

```shell
curl -X POST http://example.com/powerapi/v1/devices \
-H "Content-Type: application/json" \
-H "ts: 1723448959001" \
-H "accessKey: dummy" \
-H "sign: ****************************************************************" \
-d '{
    "devices":  [
                    {
                        "id": "12345",
                        "price": 100,
                        "price_unit": 2,
                        "device_number": 50
                    },
                    {
                        "id": "12346",
                        "price": 200,
                        "price_unit": 3,
                        "device_number": 30
                    },
                    {
                        "id": "12347",
                        "price": 150,
                        "price_unit": 1,
                        "device_number": 20
                    }
                ]
}'

```

```go
//请求结构体
type DeviceReq struct {
	Devices []DeviceItem `json:"devices"`
}
type DeviceItem struct {
	Id           string `json:"id" example:"1"`
	DeviceNumber int64  `json:"device_number" example:"1"`
	Price        int    `json:"price" example:"100"`
	PriceUnit    int    `json:"price_unit" example:"1"`
}
```



> 接口请求示例

```json
{
    "devices":  [
                    {
                        "id": "12345",
                        "price": 100,
                        "price_unit": 2,
                        "device_number": 50
                    },
                    {
                        "id": "12346",
                        "price": 200,
                        "price_unit": 3,
                        "device_number": 30
                    },
                    {
                        "id": "12347",
                        "price": 150,
                        "price_unit": 1,
                        "device_number": 20
                    }
                ]
}

```

> 接口返回示例

```json
{
    "code": 200,
    "msg": "All products processed successfully."
}

```

算力接入方使用该接口更新产品数据。 


### 请求路径

`POST /powerapi/v1/devices`

### URL参数

参数 | 描述 | 类型 | 必填
--------- | ------- |  ------- | -----------
无 |   |  |

### payload说明



| 字段    | 描述             | 类型     | 必填 |
| ------- | ---------------- | -------- | ---- |
| devices | 算力产品对象数组 | []device | 是   |

#### device 对象

字段 | 描述 | 类型 | 必填
--------- | ------- | ------- | -----------
id | 审核通过后的算力产品id | string | 是
price | 算力产品价格 | int | 否
price_unit | 算力产品计价单位 [时: 1, 天: 2, 月: 3] | int | 否
device_number | 算力产品库存数量 | int | 否

<aside class="notice">
可以定时重复调用，也可以在变化的时候调用。若是前者建议频率十秒级，若是后者则需要不断尝试调用直到得到成功响应。
</aside>

## 数据中心集群（区域）算力统计（优先级P0）

```shell
curl -X POST http://example.com/powerapi/v1/clusters \
-H "Content-Type: application/json" \
-H "ts: 1723448959001" \
-H "accessKey: dummy" \
-H "sign: ****************************************************************" \
-d '{
    "clusters":  [
                    {
                        "id": "23456",
                        "ping": 10,
                        "total_flops": 29600,
                        "used_flops": 0,
                        "available_flops": 29600,
                        "total_cpu_cores": 2038,
                        "used_cpu_cores": 0,
                        "available_cpu_cores": 2038,
                        "total_mem_size": 12000,
                        "used_mem_size": 0,
                        "available_mem_size": 12000,
                        "total_gpu_cards": 80,
                        "used_gpu_cards": 0,
                        "available_gpu_cards": 80,
                        "total_gpu_mem_size": 6000,
                        "used_gpu_mem_size": 0,
                        "available_gpu_mem_size": 6000,
                        "total_storage_size": 50000,
                        "used_storage_size": 10000,
                        "available_storage_size": 40000 
                    },
                    {
                        "id": "23457",
                        "ping": 11,
                        "total_flops": 2960,
                        "used_flops": 0,
                        "available_flops": 2960,
                        "total_cpu_cores": 2038,
                        "used_cpu_cores": 0,
                        "available_cpu_cores": 2038,
                        "total_mem_size": 12000,
                        "used_mem_size": 0,
                        "available_mem_size": 12000,
                        "total_gpu_cards": 80,
                        "used_gpu_cards": 0,
                        "available_gpu_cards": 80,
                        "total_gpu_mem_size": 6000,
                        "used_gpu_mem_size": 0,
                        "available_gpu_mem_size": 6000,
                        "total_storage_size": 50000,
                        "used_storage_size": 10000,
                        "available_storage_size": 40000 
                    }
                ]
}'
```

```go
// 请求结构体
type ClusterReq struct {
	Clusters []ClusterItem `json:"clusters"`
}

type ClusterItem struct {
	Id   string `json:"id"` //cluster id
	Ping int    `json:"ping"`

	TotalFlops     int `json:"total_flops"`     // 单位TFOPS(FP16非稀疏)
	UsedFlops      int `json:"used_flops"`      // 单位TFOPS(FP16非稀疏)
	AvailableFlops int `json:"available_flops"` // 单位TFOPS(FP16非稀疏)

	TotalCpuCores     int `json:"total_cpu_cores"`
	UsedCpuCores      int `json:"used_cpu_cores"`
	AvailableCpuCores int `json:"available_cpu_cores"`

	TotalMemSize     int `json:"total_mem_size"`     // 单位GB
	UsedMemSize      int `json:"used_mem_size"`      // 单位GB
	AvailableMemSize int `json:"available_mem_size"` // 单位GB

	TotalGpuCards     int `json:"total_gpu_cards"`     // 单位张
	UsedGpuCards      int `json:"used_gpu_cards"`      // 单位张
	AvailableGpuCards int `json:"available_gpu_cards"` // 单位张

	TotalGpuMemSize     int `json:"total_gpu_mem_size"`     // 单位GB
	UsedGpuMemSize      int `json:"used_gpu_mem_size"`      // 单位GB
	AvailableGpuMemSize int `json:"available_gpu_mem_size"` // 单位GB

	TotalStorageSize     int `json:"total_storage_size"`     // 单位GB
	UsedStorageSize      int `json:"used_storage_size"`      // 单位GB
	AvailableStorageSize int `json:"available_storage_size"` // 单位GB
}
```

> 接口请求示例
```
{
   "clusters":  [
                    {
                        "id": "23456",
                        "ping": 10,
                        "total_flops": 29600,
                        "used_flops": 0,
                        "available_flops": 29600,
                        "total_cpu_cores": 2038,
                        "used_cpu_cores": 0,
                        "available_cpu_cores": 2038,
                        "total_mem_size": 12000,
                        "used_mem_size": 0,
                        "available_mem_size": 12000,
                        "total_gpu_cards": 80,
                        "used_gpu_cards": 0,
                        "available_gpu_cards": 80,
                        "total_gpu_mem_size": 6000,
                        "used_gpu_mem_size": 0,
                        "available_gpu_mem_size": 6000,
                        "total_storage_size": 50000,
                        "used_storage_size": 10000,
                        "available_storage_size": 40000 
                    },
                    {
                        "id": "23457",
                        "ping": 11,
                        "total_flops": 2960,
                        "used_flops": 0,
                        "available_flops": 2960,
                        "total_cpu_cores": 2038,
                        "used_cpu_cores": 0,
                        "available_cpu_cores": 2038,
                        "total_mem_size": 12000,
                        "used_mem_size": 0,
                        "available_mem_size": 12000,
                        "total_gpu_cards": 80,
                        "used_gpu_cards": 0,
                        "available_gpu_cards": 80,
                        "total_gpu_mem_size": 6000,
                        "used_gpu_mem_size": 0,
                        "available_gpu_mem_size": 6000,
                        "total_storage_size": 50000,
                        "used_storage_size": 10000,
                        "available_storage_size": 40000 
                    }
                ]
}
```
> 接口返回示例

```json
{
    "code": 200,
    "msg": "succ"
}
```

接口描述： 更新产品接入所在数据中心集群（区域）算力统计，包含网络状况、接入的总算力、当前已用算力和空闲算力规模。 


### 请求路径

`POST /powerapi/v1/clusters`

### URL参数


参数 | 描述 | 类型 | 必填
--------- | ------- |  ------- | -----------
无 |   |  |

### payload说明

| 字段     | 描述                 | 类型      | 必填 |
| -------- | -------------------- | --------- | ---- |
| clusters | 数据中心集群对象数组 | []cluster | 是   |

#### cluster 对象

字段 | 描述 | 类型 | 必填
--------- | ------- | ------- | -----------
id | 数据中心集群 id | string | 是
ping | ping值，代表机房的访问速度，用于前端展现。目前对该字段要求不严格。 | int | 是
total_flops | 该数据中心集群接入的GPU总算力，单位 TFLOPS（f32） | int | 是
used_flops | 该数据中心集群接入的GPU已使用算力，单位 TFLOPS（f32） | int | 是
available_flops | 该数据中心集群接入的GPU空闲算力，单位 TFLOPS（f32） | int | 是
total_cpu_cores | 该数据中心集群接入的总cpu核数 | int | 是 
used_cpu_cores | 该数据中心集群接入的已使用cpu核数 | int | 是 
available_cpu_cores | 该数据中心集群接入的空闲cpu核数 | int | 是 
total_mem_size | 该数据中心集群接入的总内存，单位GB | int | 是 
used_mem_size | 该数据中心集群接入的已使用内存，单位GB | int | 是 
available_mem_size | 该数据中心集群接入的空闲内存，单位GB | int | 是 
total_gpu_cards | 该数据中心集群接入的总卡数，单位张 | int | 是 
used_gpu_cards | 该数据中心集群接入的已使用卡数，单位张 | int | 是 
available_gpu_cards | 该数据中心集群接入的空闲卡数，单位张 | int | 是 
total_gpu_mem_size | 该数据中心集群接入的总显卡内存，单位GB | int | 是 
used_gpu_mem_size | 该数据中心集群接入的已使用显卡内存，单位GB | int | 是 
available_gpu_mem_size | 该数据中心集群接入的空闲显卡内存，单位GB | int | 是 
total_storage_size | 该数据中心集群接入的总存储容量，单位GB | int | 是 
used_storage_size | 该数据中心集群接入的已使用存储容量，单位GB | int | 是 
available_storage_size | 该数据中心集群接入的空闲存储容量，单位GB | int | 是 



<aside class="notice">
可以定时重复调用，也可以在变化的时候调用。若是前者建议频率十秒级，若是后者则需要不断尝试调用直到得到成功响应。
</aside>